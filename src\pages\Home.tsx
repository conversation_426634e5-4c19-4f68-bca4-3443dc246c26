import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { Heart, Users, UserPlus, <PERSON>, Sparkles } from 'lucide-react'
import RelationshipTypeSelector from '../components/RelationshipTypeSelector'

export default function Home() {
  const [showSelector, setShowSelector] = useState(false)
  const navigate = useNavigate()

  const handleCreateConversation = () => {
    setShowSelector(true)
  }

  const handleTypeSelected = (type: string) => {
    // Por ahora, redirigir al dashboard
    navigate('/dashboard')
  }

  return (
    <div className="min-h-screen pt-8 pb-20 md:pb-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {!showSelector ? (
          <>
            {/* Hero Section */}
            <div className="text-center mb-16">
              <div className="mb-8">
                <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-soft-pink-400 to-soft-blue-400 rounded-full mb-6">
                  <Heart className="w-10 h-10 text-white" />
                </div>
                <h1 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6 leading-tight">
                  Crea un espacio emocional para{' '}
                  <span className="bg-gradient-to-r from-soft-pink-600 to-soft-blue-600 bg-clip-text text-transparent">
                    hablar con claridad
                  </span>
                </h1>
                <p className="text-xl text-soft-lavender-600 mb-8 max-w-2xl mx-auto leading-relaxed">
                  Una conversación guiada, un paso hacia el entendimiento con quien más importa.
                </p>
              </div>

              {/* CTA Button */}
              <button
                onClick={handleCreateConversation}
                className="btn-primary text-lg px-8 py-4 mb-12 inline-flex items-center space-x-3"
              >
                <Sparkles className="w-6 h-6" />
                <span>Crear una conversación emocional</span>
              </button>
            </div>

            {/* Features Preview */}
            <div className="grid md:grid-cols-3 gap-8 mb-16">
              <div className="card text-center">
                <div className="w-12 h-12 bg-soft-pink-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Heart className="w-6 h-6 text-soft-pink-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-800 mb-2">
                  Conversaciones Profundas
                </h3>
                <p className="text-soft-lavender-600">
                  Herramientas guiadas para facilitar diálogos significativos y auténticos.
                </p>
              </div>

              <div className="card text-center">
                <div className="w-12 h-12 bg-soft-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Users className="w-6 h-6 text-soft-blue-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-800 mb-2">
                  Inteligencia Emocional
                </h3>
                <p className="text-soft-lavender-600">
                  Análisis emocional y recomendaciones personalizadas para cada relación.
                </p>
              </div>

              <div className="card text-center">
                <div className="w-12 h-12 bg-soft-lavender-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Sparkles className="w-6 h-6 text-soft-lavender-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-800 mb-2">
                  Reconciliación
                </h3>
                <p className="text-soft-lavender-600">
                  Espacios seguros para sanar y fortalecer vínculos importantes.
                </p>
              </div>
            </div>

            {/* Quick Preview of Relationship Types */}
            <div className="text-center">
              <h2 className="text-2xl font-semibold text-gray-800 mb-8">
                Para todo tipo de relaciones
              </h2>
              <div className="flex flex-wrap justify-center gap-4">
                {[
                  { icon: Heart, label: 'Pareja', color: 'soft-pink' },
                  { icon: Users, label: 'Amistad', color: 'soft-blue' },
                  { icon: Baby, label: 'Familiar', color: 'soft-lavender' },
                  { icon: UserPlus, label: 'Alguien nuevo', color: 'soft-pink' },
                  { icon: Heart, label: 'Expareja', color: 'soft-blue' }
                ].map((type, index) => (
                  <div
                    key={index}
                    className={`flex items-center space-x-2 px-4 py-2 bg-${type.color}-50 text-${type.color}-700 rounded-full border border-${type.color}-200`}
                  >
                    <type.icon className="w-4 h-4" />
                    <span className="text-sm font-medium">{type.label}</span>
                  </div>
                ))}
              </div>
            </div>
          </>
        ) : (
          <RelationshipTypeSelector
            onSelect={handleTypeSelected}
            onBack={() => setShowSelector(false)}
          />
        )}
      </div>
    </div>
  )
}
