import { Heart, Users, Baby, UserPlus, ArrowLeft } from 'lucide-react'

interface RelationshipTypeSelectorProps {
  onSelect: (type: string) => void
  onBack: () => void
}

const relationshipTypes = [
  {
    id: 'pareja',
    title: 'Pareja',
    description: 'Fortalece tu relación romántica con conversaciones profundas',
    icon: Heart,
    color: 'soft-pink',
    gradient: 'from-soft-pink-400 to-soft-pink-600'
  },
  {
    id: 'amistad',
    title: 'Amistad',
    description: 'Profundiza vínculos de amistad y resuelve malentendidos',
    icon: Users,
    color: 'soft-blue',
    gradient: 'from-soft-blue-400 to-soft-blue-600'
  },
  {
    id: 'familiar',
    title: 'Familiar',
    description: 'Mejora la comunicación con familiares importantes',
    icon: Baby,
    color: 'soft-lavender',
    gradient: 'from-soft-lavender-400 to-soft-lavender-600'
  },
  {
    id: 'nuevo',
    title: 'Alguien nuevo',
    description: 'Conoce mejor a alguien especial en tu vida',
    icon: UserPlus,
    color: 'soft-pink',
    gradient: 'from-soft-pink-300 to-soft-blue-400'
  },
  {
    id: 'expareja',
    title: 'Expareja',
    description: 'Busca reconciliación o cierre emocional saludable',
    icon: Heart,
    color: 'soft-blue',
    gradient: 'from-soft-blue-300 to-soft-lavender-400'
  }
]

export default function RelationshipTypeSelector({ onSelect, onBack }: RelationshipTypeSelectorProps) {
  return (
    <div className="max-w-4xl mx-auto">
      {/* Header */}
      <div className="flex items-center mb-8">
        <button
          onClick={onBack}
          className="flex items-center space-x-2 text-soft-lavender-600 hover:text-soft-pink-600 transition-colors"
        >
          <ArrowLeft className="w-5 h-5" />
          <span>Volver</span>
        </button>
      </div>

      <div className="text-center mb-12">
        <h2 className="text-3xl font-bold text-gray-800 mb-4">
          ¿Qué tipo de relación quieres fortalecer?
        </h2>
        <p className="text-lg text-soft-lavender-600 max-w-2xl mx-auto">
          Selecciona el tipo de vínculo para personalizar tu experiencia de conversación emocional.
        </p>
      </div>

      {/* Relationship Type Cards */}
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        {relationshipTypes.map((type) => (
          <button
            key={type.id}
            onClick={() => onSelect(type.id)}
            className="card text-left hover:scale-105 transition-all duration-300 group"
          >
            <div className={`w-16 h-16 bg-gradient-to-r ${type.gradient} rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}>
              <type.icon className="w-8 h-8 text-white" />
            </div>
            
            <h3 className="text-xl font-semibold text-gray-800 mb-2">
              {type.title}
            </h3>
            
            <p className="text-soft-lavender-600 leading-relaxed">
              {type.description}
            </p>

            <div className="mt-4 flex items-center text-sm font-medium text-soft-pink-600 group-hover:text-soft-blue-600 transition-colors">
              <span>Comenzar conversación</span>
              <ArrowLeft className="w-4 h-4 ml-2 rotate-180 group-hover:translate-x-1 transition-transform" />
            </div>
          </button>
        ))}
      </div>

      {/* Additional Info */}
      <div className="mt-12 text-center">
        <div className="card max-w-2xl mx-auto bg-gradient-to-r from-soft-pink-50 to-soft-blue-50 border border-soft-pink-200">
          <div className="flex items-center justify-center w-12 h-12 bg-gradient-to-r from-soft-pink-400 to-soft-blue-400 rounded-full mx-auto mb-4">
            <Heart className="w-6 h-6 text-white" />
          </div>
          <h3 className="text-lg font-semibold text-gray-800 mb-2">
            ¿No estás seguro?
          </h3>
          <p className="text-soft-lavender-600 mb-4">
            Puedes cambiar el tipo de relación en cualquier momento. Lo importante es dar el primer paso.
          </p>
          <button className="btn-secondary">
            Explorar todas las opciones
          </button>
        </div>
      </div>
    </div>
  )
}
