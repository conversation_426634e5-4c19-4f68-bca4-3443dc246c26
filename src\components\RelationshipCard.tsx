import { Message<PERSON>ir<PERSON>, Bar<PERSON><PERSON>3, Send, Edit3, Clock } from 'lucide-react'

interface Relationship {
  id: number
  name: string
  type: string
  status: string
  lastActivity: string
  emotionalScore: number
  responseTime: string
  dominantEmotion: string
}

interface RelationshipCardProps {
  relationship: Relationship
  getTypeIcon: (type: string) => any
  getStatusColor: (status: string) => string
  getStatusText: (status: string) => string
}

export default function RelationshipCard({ 
  relationship, 
  getTypeIcon, 
  getStatusColor, 
  getStatusText 
}: RelationshipCardProps) {
  const TypeIcon = getTypeIcon(relationship.type)

  const getEmotionEmoji = (emotion: string) => {
    switch (emotion) {
      case 'alegría': return '😊'
      case 'nostalgia': return '😌'
      case 'amor': return '🥰'
      case 'tristeza': return '😢'
      case 'esperanza': return '🌟'
      default: return '😊'
    }
  }

  const getEmotionColor = (score: number) => {
    if (score >= 80) return 'text-green-600 bg-green-100'
    if (score >= 60) return 'text-yellow-600 bg-yellow-100'
    return 'text-red-600 bg-red-100'
  }

  return (
    <div className="card hover:scale-105 transition-all duration-300">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="w-12 h-12 bg-gradient-to-r from-soft-pink-400 to-soft-blue-400 rounded-full flex items-center justify-center">
            <TypeIcon className="w-6 h-6 text-white" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-800">{relationship.name}</h3>
            <p className="text-sm text-soft-lavender-600 capitalize">{relationship.type}</p>
          </div>
        </div>
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(relationship.status)}`}>
          {getStatusText(relationship.status)}
        </span>
      </div>

      {/* Emotional Metrics */}
      <div className="space-y-3 mb-4">
        <div className="flex items-center justify-between">
          <span className="text-sm text-soft-lavender-600">Puntuación emocional</span>
          <div className="flex items-center space-x-2">
            <div className={`w-2 h-2 rounded-full ${getEmotionColor(relationship.emotionalScore).replace('text-', 'bg-').replace(' bg-', ' ')}`}></div>
            <span className="text-sm font-medium text-gray-800">{relationship.emotionalScore}%</span>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <span className="text-sm text-soft-lavender-600">Emoción dominante</span>
          <div className="flex items-center space-x-1">
            <span className="text-sm">{getEmotionEmoji(relationship.dominantEmotion)}</span>
            <span className="text-sm font-medium text-gray-800 capitalize">{relationship.dominantEmotion}</span>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <span className="text-sm text-soft-lavender-600">Tiempo de respuesta</span>
          <div className="flex items-center space-x-1">
            <Clock className="w-3 h-3 text-soft-lavender-500" />
            <span className="text-sm font-medium text-gray-800">{relationship.responseTime}</span>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <span className="text-sm text-soft-lavender-600">Última actividad</span>
          <span className="text-sm font-medium text-gray-800">{relationship.lastActivity}</span>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="mb-4">
        <div className="flex items-center justify-between mb-1">
          <span className="text-xs text-soft-lavender-600">Progreso de conversación</span>
          <span className="text-xs font-medium text-gray-800">{relationship.emotionalScore}%</span>
        </div>
        <div className="w-full bg-soft-lavender-100 rounded-full h-2">
          <div 
            className="bg-gradient-to-r from-soft-pink-400 to-soft-blue-400 h-2 rounded-full transition-all duration-300"
            style={{ width: `${relationship.emotionalScore}%` }}
          ></div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex space-x-2">
        <button className="flex-1 btn-secondary text-sm py-2 flex items-center justify-center space-x-1">
          <BarChart3 className="w-4 h-4" />
          <span>Ver informe</span>
        </button>
        <button className="flex-1 btn-primary text-sm py-2 flex items-center justify-center space-x-1">
          <MessageCircle className="w-4 h-4" />
          <span>Continuar</span>
        </button>
      </div>

      {/* Quick Actions */}
      <div className="flex justify-between mt-3 pt-3 border-t border-soft-lavender-100">
        <button className="flex items-center space-x-1 text-xs text-soft-lavender-600 hover:text-soft-pink-600 transition-colors">
          <Send className="w-3 h-3" />
          <span>Enviar mensaje</span>
        </button>
        <button className="flex items-center space-x-1 text-xs text-soft-lavender-600 hover:text-soft-blue-600 transition-colors">
          <Edit3 className="w-3 h-3" />
          <span>Editar</span>
        </button>
      </div>
    </div>
  )
}
