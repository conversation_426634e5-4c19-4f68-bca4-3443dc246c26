import { ReactNode } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { Heart, Home, BarChart3, User } from 'lucide-react'

interface LayoutProps {
  children: ReactNode
}

export default function Layout({ children }: LayoutProps) {
  const location = useLocation()

  const isActive = (path: string) => location.pathname === path

  return (
    <div className="min-h-screen bg-gradient-to-br from-soft-pink-50 via-white to-soft-blue-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-sm border-b border-soft-lavender-100 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <Link to="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-soft-pink-400 to-soft-blue-400 rounded-full flex items-center justify-center">
                <Heart className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-semibold bg-gradient-to-r from-soft-pink-600 to-soft-blue-600 bg-clip-text text-transparent">
                SmileLink
              </span>
            </Link>

            {/* Navigation */}
            <nav className="hidden md:flex space-x-8">
              <Link
                to="/"
                className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-all duration-200 ${
                  isActive('/') 
                    ? 'bg-soft-pink-100 text-soft-pink-700' 
                    : 'text-soft-lavender-600 hover:text-soft-pink-600 hover:bg-soft-pink-50'
                }`}
              >
                <Home className="w-4 h-4" />
                <span>Inicio</span>
              </Link>
              <Link
                to="/dashboard"
                className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-all duration-200 ${
                  isActive('/dashboard') 
                    ? 'bg-soft-blue-100 text-soft-blue-700' 
                    : 'text-soft-lavender-600 hover:text-soft-blue-600 hover:bg-soft-blue-50'
                }`}
              >
                <BarChart3 className="w-4 h-4" />
                <span>Dashboard</span>
              </Link>
            </nav>

            {/* User Menu */}
            <div className="flex items-center space-x-4">
              <button className="p-2 rounded-full bg-soft-lavender-100 text-soft-lavender-600 hover:bg-soft-lavender-200 transition-colors">
                <User className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1">
        {children}
      </main>

      {/* Mobile Navigation */}
      <nav className="md:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-soft-lavender-100 px-4 py-2">
        <div className="flex justify-around">
          <Link
            to="/"
            className={`flex flex-col items-center space-y-1 p-2 rounded-lg ${
              isActive('/') ? 'text-soft-pink-600' : 'text-soft-lavender-500'
            }`}
          >
            <Home className="w-5 h-5" />
            <span className="text-xs">Inicio</span>
          </Link>
          <Link
            to="/dashboard"
            className={`flex flex-col items-center space-y-1 p-2 rounded-lg ${
              isActive('/dashboard') ? 'text-soft-blue-600' : 'text-soft-lavender-500'
            }`}
          >
            <BarChart3 className="w-5 h-5" />
            <span className="text-xs">Dashboard</span>
          </Link>
        </div>
      </nav>
    </div>
  )
}
