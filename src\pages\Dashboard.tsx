import { useState } from 'react'
import { <PERSON>, <PERSON>, Baby, UserPlus, MessageCircle, BarChart3, Clock, Smile, Plus, Send, Edit3 } from 'lucide-react'
import RelationshipCard from '../components/RelationshipCard'

// Mock data para demostración
const mockRelationships = [
  {
    id: 1,
    name: '<PERSON>',
    type: 'pareja',
    status: 'en-proceso',
    lastActivity: '2 días',
    emotionalScore: 85,
    responseTime: '4 horas',
    dominantEmotion: 'alegría'
  },
  {
    id: 2,
    name: '<PERSON>',
    type: 'amistad',
    status: 'pendiente',
    lastActivity: '1 semana',
    emotionalScore: 72,
    responseTime: '1 día',
    dominantEmotion: 'nostalgia'
  },
  {
    id: 3,
    name: '<PERSON><PERSON><PERSON>',
    type: 'familiar',
    status: 'finalizado',
    lastActivity: '3 días',
    emotionalScore: 92,
    responseTime: '30 min',
    dominantEmotion: 'amor'
  }
]

const emotionalStats = {
  averageResponseTime: '2.5 horas',
  totalConversations: 12,
  completedConversations: 8,
  averageEmotionalScore: 83
}

export default function Dashboard() {
  const [relationships] = useState(mockRelationships)

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'pareja': return Heart
      case 'amistad': return Users
      case 'familiar': return Baby
      case 'nuevo': return UserPlus
      default: return Heart
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pendiente': return 'text-yellow-600 bg-yellow-100'
      case 'en-proceso': return 'text-blue-600 bg-blue-100'
      case 'finalizado': return 'text-green-600 bg-green-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pendiente': return 'Pendiente'
      case 'en-proceso': return 'En proceso'
      case 'finalizado': return 'Finalizado'
      default: return 'Desconocido'
    }
  }

  return (
    <div className="min-h-screen pt-8 pb-20 md:pb-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-800 mb-2">
              Tu Dashboard Emocional
            </h1>
            <p className="text-soft-lavender-600">
              Gestiona tus conversaciones y observa el progreso de tus relaciones
            </p>
          </div>
          <button className="btn-primary mt-4 md:mt-0 inline-flex items-center space-x-2">
            <Plus className="w-5 h-5" />
            <span>Nueva conversación</span>
          </button>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
          <div className="card text-center">
            <div className="w-12 h-12 bg-soft-pink-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <MessageCircle className="w-6 h-6 text-soft-pink-600" />
            </div>
            <div className="text-2xl font-bold text-gray-800">{emotionalStats.totalConversations}</div>
            <div className="text-sm text-soft-lavender-600">Conversaciones</div>
          </div>

          <div className="card text-center">
            <div className="w-12 h-12 bg-soft-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <BarChart3 className="w-6 h-6 text-soft-blue-600" />
            </div>
            <div className="text-2xl font-bold text-gray-800">{emotionalStats.averageEmotionalScore}%</div>
            <div className="text-sm text-soft-lavender-600">Puntuación emocional</div>
          </div>

          <div className="card text-center">
            <div className="w-12 h-12 bg-soft-lavender-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <Clock className="w-6 h-6 text-soft-lavender-600" />
            </div>
            <div className="text-2xl font-bold text-gray-800">{emotionalStats.averageResponseTime}</div>
            <div className="text-sm text-soft-lavender-600">Tiempo promedio</div>
          </div>

          <div className="card text-center">
            <div className="w-12 h-12 bg-gradient-to-r from-soft-pink-100 to-soft-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <Smile className="w-6 h-6 text-soft-pink-600" />
            </div>
            <div className="text-2xl font-bold text-gray-800">{emotionalStats.completedConversations}</div>
            <div className="text-sm text-soft-lavender-600">Completadas</div>
          </div>
        </div>

        {/* Relationships Grid */}
        <div className="mb-8">
          <h2 className="text-2xl font-semibold text-gray-800 mb-6">
            Tus Relaciones
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {relationships.map((relationship) => (
              <RelationshipCard
                key={relationship.id}
                relationship={relationship}
                getTypeIcon={getTypeIcon}
                getStatusColor={getStatusColor}
                getStatusText={getStatusText}
              />
            ))}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid md:grid-cols-2 gap-6">
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">
              Tests Emocionales
            </h3>
            <p className="text-soft-lavender-600 mb-4">
              Descubre más sobre tus emociones y las de otros
            </p>
            <div className="space-y-3">
              <button className="w-full btn-secondary text-left flex items-center justify-between">
                <span>¿Qué siento por alguien?</span>
                <Heart className="w-4 h-4" />
              </button>
              <button className="w-full btn-secondary text-left flex items-center justify-between">
                <span>¿Qué siente alguien por mí?</span>
                <Users className="w-4 h-4" />
              </button>
            </div>
          </div>

          <div className="card">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">
              Herramientas de Crecimiento
            </h3>
            <p className="text-soft-lavender-600 mb-4">
              Recursos para tu desarrollo personal
            </p>
            <div className="space-y-3">
              <button className="w-full btn-secondary text-left flex items-center justify-between">
                <span>Autoevaluación de vida</span>
                <BarChart3 className="w-4 h-4" />
              </button>
              <button className="w-full btn-secondary text-left flex items-center justify-between">
                <span>Recomendaciones de libros</span>
                <Smile className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
