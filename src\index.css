@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  font-family: 'Inter', system-ui, sans-serif;
  line-height: 1.6;
  font-weight: 400;
  color-scheme: light;
  color: #1f2937;
  background-color: #fafafa;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  min-height: 100vh;
  background: linear-gradient(135deg, #fdf2f8 0%, #eff6ff 100%);
}

/* Componentes personalizados */
@layer components {
  .btn-primary {
    @apply bg-gradient-to-r from-soft-pink-400 to-soft-blue-400 text-white font-medium py-3 px-6 rounded-2xl shadow-gentle hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1;
  }

  .btn-secondary {
    @apply bg-white text-soft-lavender-600 font-medium py-3 px-6 rounded-2xl shadow-soft hover:shadow-gentle transition-all duration-300 border border-soft-lavender-200;
  }

  .card {
    @apply bg-white rounded-3xl shadow-soft hover:shadow-gentle transition-all duration-300 p-6;
  }

  .input-field {
    @apply w-full px-4 py-3 rounded-xl border border-soft-lavender-200 focus:border-soft-pink-400 focus:ring-2 focus:ring-soft-pink-100 transition-all duration-200 outline-none;
  }
}
